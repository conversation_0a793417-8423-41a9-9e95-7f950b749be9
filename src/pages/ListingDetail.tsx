import React from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { 
  ArrowLeft,
  Share2,
  Heart,
  Download,
  Edit3,
  Eye,
  TrendingUp,
  DollarSign,
  Users,
  Calendar,
  MapPin,
  Building2,
  Briefcase,
  Target,
  Clock,
  CheckCircle,
  AlertTriangle,
  Star,
  MoreVertical
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { formatCurrencySafe } from "@/lib/formatters";
import { useListing, useUpdateListingStatusMutation } from "@/hooks/useQueryApi";
import { LoadingState, Spinner } from "@/components/ui/spinner";

const V2_ListingDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  
  const { listing, loading, error, refetch } = useListing(id);
  const updateStatusMutation = useUpdateListingStatusMutation({
    onSuccess: () => refetch()
  });

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background via-background to-muted/20">
        <LoadingState
          message="Loading listing details..."
          spinnerSize="lg"
          className="text-center"
        />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background via-background to-muted/20">
        <div className="text-center max-w-md space-y-4">
          <AlertTriangle className="h-16 w-16 text-destructive mx-auto" />
          <div className="space-y-2">
            <h2 className="text-2xl font-bold">Unable to Load Listing</h2>
            <p className="text-muted-foreground">
              We encountered an issue while fetching the listing details.
            </p>
          </div>
          <div className="flex gap-3 justify-center">
            <Button onClick={() => refetch()} variant="outline">
              Try Again
            </Button>
            <Button onClick={() => navigate('/listings')}>
              Back to Listings
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (!listing) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background via-background to-muted/20">
        <div className="text-center space-y-4">
          <div className="h-16 w-16 rounded-full bg-muted flex items-center justify-center mx-auto">
            <Building2 className="h-8 w-8 text-muted-foreground" />
          </div>
          <div className="space-y-2">
            <h2 className="text-2xl font-bold">Listing Not Found</h2>
            <p className="text-muted-foreground">This listing may have been removed or doesn't exist.</p>
          </div>
          <Button onClick={() => navigate('/listings')}>
            Back to Listings
          </Button>
        </div>
      </div>
    );
  }

  const statusConfig = {
    "active": { color: "bg-emerald-500", label: "Active", icon: CheckCircle },
    "under contract": { color: "bg-amber-500", label: "Under Contract", icon: Clock },
    "sold": { color: "bg-green-600", label: "Sold", icon: CheckCircle },
    "confidential": { color: "bg-purple-600", label: "Confidential", icon: Eye },
    "archived": { color: "bg-slate-500", label: "Archived", icon: MoreVertical },
    "draft": { color: "bg-blue-500", label: "Draft", icon: Edit3 }
  };

  const currentStatus = statusConfig[listing.status?.toLowerCase() as keyof typeof statusConfig] || statusConfig["active"];
  const StatusIcon = currentStatus.icon;

  const handleStatusUpdate = (status: string) => {
    updateStatusMutation.mutate({
      listingId: listing.id,
      statusUpdate: { status }
    });
  };

  // Calculate metrics
  const askingPrice = listing.askingPrice ?? 0;
  const cashFlow = listing.cashFlowSde ?? 0;
  const annualRevenue = listing.annualRevenue ?? 0;

  const multiple = cashFlow > 0 ? (askingPrice / cashFlow).toFixed(1) : 'N/A';
  const roi = askingPrice > 0 ? ((cashFlow / askingPrice) * 100).toFixed(1) : 'N/A';

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Hero Header */}
      <div className="relative overflow-hidden border-b bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5">
        <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-primary/5" />
        <div className="relative max-w-7xl mx-auto px-6 py-8">
          <div className="flex items-center gap-4 mb-6">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/listings')}
              className="text-muted-foreground hover:text-foreground"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Listings
            </Button>
          </div>

          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
            <div className="space-y-4 flex-1">
              <div className="flex items-center gap-3 flex-wrap">
                <Badge 
                  className={`${currentStatus.color} text-white border-0 px-3 py-1`}
                  variant="secondary"
                >
                  <StatusIcon className="h-3 w-3 mr-1" />
                  {currentStatus.label}
                </Badge>
                <span className="text-xs text-muted-foreground">•</span>
                <span className="text-sm text-muted-foreground">ID: {listing.id}</span>
              </div>

              <div>
                <h1 className="text-4xl font-bold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent mb-3">
                  {listing.businessName}
                </h1>
                <div className="flex items-center gap-4 text-muted-foreground flex-wrap">
                  <div className="flex items-center gap-1">
                    <Briefcase className="h-4 w-4" />
                    <span>{listing.industry}</span>
                  </div>
                  <span>•</span>
                  <div className="flex items-center gap-1">
                    <MapPin className="h-4 w-4" />
                    <span>{listing.generalLocation}</span>
                  </div>
                  {listing.yearEstablished && (
                    <>
                      <span>•</span>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        <span>Est. {listing.yearEstablished}</span>
                      </div>
                    </>
                  )}
                </div>
              </div>

              {/* Key Metrics Bar */}
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 pt-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">
                    {formatCurrencySafe(listing.askingPrice)}
                  </div>
                  <div className="text-xs text-muted-foreground">Asking Price</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-emerald-600">
                    {formatCurrencySafe(listing.cashFlowSde)}
                  </div>
                  <div className="text-xs text-muted-foreground">Cash Flow</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {multiple}x
                  </div>
                  <div className="text-xs text-muted-foreground">Multiple</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    {roi}%
                  </div>
                  <div className="text-xs text-muted-foreground">ROI</div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-3 lg:flex-col lg:items-stretch">
              <Button
                onClick={() => navigate(`/listings/${id}/edit`)}
                className="flex-1 lg:flex-none"
              >
                <Edit3 className="h-4 w-4 mr-2" />
                Edit Listing
              </Button>
              
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Heart className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="sm">
                  <Share2 className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4" />
                </Button>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" disabled={updateStatusMutation.isPending}>
                      {updateStatusMutation.isPending ? (
                        <Spinner size="sm" />
                      ) : (
                        <MoreVertical className="h-4 w-4" />
                      )}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    <DropdownMenuItem onClick={() => handleStatusUpdate('active')}>
                      <CheckCircle className="h-4 w-4 mr-2 text-emerald-500" />
                      Mark Active
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleStatusUpdate('under contract')}>
                      <Clock className="h-4 w-4 mr-2 text-amber-500" />
                      Under Contract
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleStatusUpdate('sold')}>
                      <Star className="h-4 w-4 mr-2 text-green-600" />
                      Mark Sold
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => handleStatusUpdate('confidential')}>
                      <Eye className="h-4 w-4 mr-2 text-purple-600" />
                      Mark Confidential
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleStatusUpdate('archived')}>
                      <MoreVertical className="h-4 w-4 mr-2 text-slate-500" />
                      Archive
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Status Update Feedback */}
      {updateStatusMutation.error && (
        <div className="max-w-7xl mx-auto px-6 py-4">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Failed to update listing status. Please try again.
            </AlertDescription>
          </Alert>
        </div>
      )}

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        <Tabs defaultValue="overview" className="space-y-8">
          <TabsList className="grid w-full grid-cols-4 lg:w-auto">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="financials">Financials</TabsTrigger>
            <TabsTrigger value="operations">Operations</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-8">
            <div className="grid gap-8 lg:grid-cols-3">
              {/* Business Overview */}
              <div className="lg:col-span-2 space-y-6">
                <Card className="overflow-hidden">
                  <CardHeader className="bg-gradient-to-r from-primary/5 to-primary/10">
                    <CardTitle className="flex items-center gap-2">
                      <Building2 className="h-5 w-5 text-primary" />
                      Business Overview
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-6 space-y-4">
                    <p className="text-muted-foreground leading-relaxed">
                      {listing.description || listing.details?.businessDescription || 
                        `${listing.businessName} is a well-established ${listing.industry?.toLowerCase()} business located in ${listing.generalLocation}. This presents an excellent opportunity for an investor or entrepreneur looking to acquire a profitable, established operation.`}
                    </p>
                    
                    <div className="grid gap-4 sm:grid-cols-2">
                      <div className="space-y-1">
                        <div className="text-sm font-medium">Location</div>
                        <div className="text-sm text-muted-foreground">{listing.generalLocation}</div>
                      </div>
                      <div className="space-y-1">
                        <div className="text-sm font-medium">Industry</div>
                        <div className="text-sm text-muted-foreground">{listing.industry}</div>
                      </div>
                      {listing.employees && (
                        <div className="space-y-1">
                          <div className="text-sm font-medium">Team Size</div>
                          <div className="text-sm text-muted-foreground">{listing.employees} employees</div>
                        </div>
                      )}
                      {listing.ownerHoursWeek && (
                        <div className="space-y-1">
                          <div className="text-sm font-medium">Owner Involvement</div>
                          <div className="text-sm text-muted-foreground">{listing.ownerHoursWeek} hours/week</div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Growth Opportunities */}
                <Card className="overflow-hidden">
                  <CardHeader className="bg-gradient-to-r from-emerald-500/5 to-emerald-500/10">
                    <CardTitle className="flex items-center gap-2">
                      <Target className="h-5 w-5 text-emerald-600" />
                      Growth Opportunities
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-6">
                    <div className="grid gap-4 sm:grid-cols-2">
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <div className="h-2 w-2 rounded-full bg-emerald-500" />
                          <span className="text-sm">Market expansion potential</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="h-2 w-2 rounded-full bg-emerald-500" />
                          <span className="text-sm">Service line extensions</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="h-2 w-2 rounded-full bg-emerald-500" />
                          <span className="text-sm">Operational efficiency improvements</span>
                        </div>
                      </div>
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <div className="h-2 w-2 rounded-full bg-blue-500" />
                          <span className="text-sm">Technology integration</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="h-2 w-2 rounded-full bg-blue-500" />
                          <span className="text-sm">Customer base expansion</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="h-2 w-2 rounded-full bg-blue-500" />
                          <span className="text-sm">Additional revenue streams</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Side Panel */}
              <div className="space-y-6">
                {/* Quick Stats */}
                <Card className="overflow-hidden">
                  <CardHeader className="bg-gradient-to-r from-blue-500/5 to-blue-500/10 pb-4">
                    <CardTitle className="flex items-center gap-2 text-base">
                      <TrendingUp className="h-4 w-4 text-blue-600" />
                      Key Metrics
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-4 space-y-4">
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">Revenue</span>
                        <span className="font-semibold">{formatCurrencySafe(listing.annualRevenue)}</span>
                      </div>
                      <Separator />
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">Cash Flow</span>
                        <span className="font-semibold text-emerald-600">{formatCurrencySafe(listing.cashFlowSde)}</span>
                      </div>
                      <Separator />
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">Multiple</span>
                        <span className="font-semibold">{multiple}x</span>
                      </div>
                      <Separator />
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">ROI Estimate</span>
                        <span className="font-semibold text-orange-600">{roi}%</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Additional Info */}
                <Card className="overflow-hidden">
                  <CardHeader className="bg-gradient-to-r from-purple-500/5 to-purple-500/10 pb-4">
                    <CardTitle className="flex items-center gap-2 text-base">
                      <Building2 className="h-4 w-4 text-purple-600" />
                      Business Details
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-4 space-y-4">
                    <div className="space-y-3">
                      {listing.yearEstablished && (
                        <>
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-muted-foreground">Established</span>
                            <span className="text-sm font-medium">{listing.yearEstablished}</span>
                          </div>
                          <Separator />
                        </>
                      )}
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">Status</span>
                        <Badge variant="outline" className="text-xs">
                          {listing.status}
                        </Badge>
                      </div>
                      <Separator />
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">Visibility</span>
                        <span className="text-sm font-medium">{listing.teamVisibility || 'All'}</span>
                      </div>
                      {listing.daysListed && (
                        <>
                          <Separator />
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-muted-foreground">Days Listed</span>
                            <span className="text-sm font-medium">{listing.daysListed}</span>
                          </div>
                        </>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="financials" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5 text-primary" />
                  Financial Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-6 md:grid-cols-2">
                  <div className="space-y-4">
                    <h4 className="font-semibold">Revenue & Profitability</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between p-3 bg-muted/30 rounded-lg">
                        <span>Annual Revenue</span>
                        <span className="font-semibold">{formatCurrencySafe(listing.annualRevenue)}</span>
                      </div>
                      <div className="flex justify-between p-3 bg-emerald-500/5 rounded-lg">
                        <span>Cash Flow (SDE)</span>
                        <span className="font-semibold text-emerald-600">{formatCurrencySafe(listing.cashFlowSde)}</span>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <h4 className="font-semibold">Investment Metrics</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between p-3 bg-primary/5 rounded-lg">
                        <span>Asking Price</span>
                        <span className="font-semibold text-primary">{formatCurrencySafe(listing.askingPrice)}</span>
                      </div>
                      <div className="flex justify-between p-3 bg-orange-500/5 rounded-lg">
                        <span>ROI Estimate</span>
                        <span className="font-semibold text-orange-600">{roi}%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="operations" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-primary" />
                  Operations & Support
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid gap-6 md:grid-cols-3">
                  <div>
                    <h4 className="font-semibold mb-3">Training & Support</h4>
                    <div className="space-y-2 text-sm">
                      <div>Training Period: {listing.details?.trainingPeriod || '30-60 days'}</div>
                      <div>Support Type: {listing.details?.supportType || 'Full operational'}</div>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-3">Real Estate</h4>
                    <div className="space-y-2 text-sm">
                      <div>Status: {listing.details?.realEstateStatus || 'Leased facility'}</div>
                      <div>Financing: {listing.details?.financingAvailable ? 'Available' : 'Contact for details'}</div>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-3">Transition</h4>
                    <div className="space-y-2 text-sm">
                      <div>Reason: {listing.details?.reasonForSale || 'Owner retirement'}</div>
                      <div>Employees: {listing.employees || 'N/A'}</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="documents" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Download className="h-5 w-5 text-primary" />
                  Documents & Files
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12 text-muted-foreground">
                  <Download className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Document management coming soon.</p>
                  <p className="text-sm">Upload and organize listing documents, financials, and presentations.</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default V2_ListingDetail;