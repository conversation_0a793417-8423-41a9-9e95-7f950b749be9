import React from 'react';
import { cn } from '@/lib/utils';
import { LoadingState } from './spinner';

interface FullPageLoadingProps {
  message?: string;
  className?: string;
  spinnerSize?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  showMessage?: boolean;
}

export const FullPageLoading: React.FC<FullPageLoadingProps> = () => {
  return (
    <div className="flex items-center justify-center min-h-screen bg-slate-50">
      <div className="flex flex-col items-center space-y-4">
        <div className="animate-spin rounded-full h-12 w-12 border-4 border-primary border-t-transparent"></div>
        <p className="text-muted-foreground">Loading...</p>
      </div>
    </div>
  );
};