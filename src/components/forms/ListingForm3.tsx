import React, { useState, useCallback, useMemo } from 'react';
import { z } from 'zod';
import { useNavigate } from 'react-router-dom';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Checkbox } from '@/components/ui/checkbox';
import { Spinner } from '@/components/ui/spinner';

import { toast } from '@/hooks/use-toast';
import { useCreateListingMutation, useUpdateListingMutation, useSaveDraftListingMutation } from '@/hooks/useQueryApi';
import type { CreateListingRequest, UpdateListingRequest, SaveDraftListingRequest } from '@/lib/api-client';
import type { ListingFormData } from '@/types';
import { listingValidationSchema } from '@/models/listingValidation';
import { parseCurrency, formatCurrencyInput } from '@/lib/formatters';
import { cn } from '@/lib/utils';

import {
  Building2,
  DollarSign,
  FileText,
  TrendingUp,
  CheckCircle,
  ArrowLeft,
  ArrowRight,
  Save,
  Eye,
} from 'lucide-react';

type FormStep = 'basic' | 'financial' | 'description' | 'review';

const industries = [
  'Restaurant',
  'Manufacturing',
  'Retail',
  'Service',
  'Healthcare',
  'Auto',
  'Technology',
  'Other',
];

const statuses = [
  { value: 'draft', label: 'Draft' },
  { value: 'active', label: 'Active' },
  { value: 'confidential', label: 'Confidential' },
  { value: 'under_contract', label: 'Under Contract' },
  { value: 'sold', label: 'Sold' },
  { value: 'expired', label: 'Expired' },
  { value: 'withdrawn', label: 'Withdrawn' },
];

export interface ListingFormProps {
  initialData?: Partial<ListingFormData>;
  isEditing?: boolean;
  listingId?: string;
}

export default function ListingForm3({ initialData = {}, isEditing = false, listingId }: ListingFormProps) {
  const navigate = useNavigate();
  const createListing = useCreateListingMutation();
  const updateListing = useUpdateListingMutation();
  const saveDraftListing = useSaveDraftListingMutation();

  const [currentStep, setCurrentStep] = useState<FormStep>('basic');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  const [formData, setFormData] = useState<ListingFormData>({
    businessName: initialData.businessName || '',
    industry: initialData.industry || '',
    askingPrice: initialData.askingPrice || '',
    cashFlow: initialData.cashFlow || '',
    status: initialData.status || 'active',
    annualRevenue: initialData.annualRevenue || '',
    location: initialData.location || '',
    yearEstablished: initialData.yearEstablished || '',
    employees: initialData.employees || '',
    ownerHours: initialData.ownerHours || '',

    businessDescription: initialData.businessDescription || '',
    briefDescription: initialData.briefDescription || '',

    revenue2023: initialData.revenue2023 || '',
    ebitda2023: initialData.ebitda2023 || '',
    inventoryValue: initialData.inventoryValue || '',
    realEstateStatus: initialData.realEstateStatus || '',
    assetsIncluded: initialData.assetsIncluded || '',
    leaseDetails: initialData.leaseDetails || '',

    businessModel: initialData.businessModel || '',
    keyFeatures: initialData.keyFeatures || '',
    competitiveAdvantages: initialData.competitiveAdvantages || '',
    customerBase: initialData.customerBase || '',

    growthOpportunities: initialData.growthOpportunities || '',
    reasonForSale: initialData.reasonForSale || '',
    trainingPeriod: initialData.trainingPeriod || '',
    supportType: initialData.supportType || '',
    financingAvailable: initialData.financingAvailable || false,

    equipmentHighlights: initialData.equipmentHighlights || '',
    supplierRelationships: initialData.supplierRelationships || '',
    keyEmployeeInfo: initialData.keyEmployeeInfo || '',
    specialNotes: initialData.specialNotes || '',
  });

  const steps = useMemo(
    () => [
      { id: 'basic' as FormStep, title: 'Basics', icon: Building2 },
      { id: 'financial' as FormStep, title: 'Financials', icon: DollarSign },
      { id: 'description' as FormStep, title: 'Description', icon: FileText },
      { id: 'review' as FormStep, title: 'Review', icon: CheckCircle },
    ],
    []
  );

  const currentStepIndex = steps.findIndex((s) => s.id === currentStep);
  const progress = ((currentStepIndex + 1) / steps.length) * 100;

  const handleInputChange = useCallback((field: keyof ListingFormData, value: string | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (validationErrors[field as string]) {
      setValidationErrors((prev) => {
        const next = { ...prev };
        delete next[field as string];
        return next;
      });
    }
  }, [validationErrors]);

  const handleCurrencyChange = useCallback((field: keyof ListingFormData, value: string) => {
    const formatted = formatCurrencyInput(value);
    setFormData((prev) => ({ ...prev, [field]: formatted }));
    if (validationErrors[field as string]) {
      setValidationErrors((prev) => {
        const next = { ...prev };
        delete next[field as string];
        return next;
      });
    }
  }, [validationErrors]);

  const validateStep = useCallback(() => {
    try {
      listingValidationSchema.parse(formData);
      setValidationErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errs: Record<string, string> = {};
        error.errors.forEach((e) => {
          if (e.path[0]) errs[e.path[0] as string] = e.message;
        });
        setValidationErrors(errs);
      }
      return false;
    }
  }, [formData]);

  const goNext = useCallback(() => {
    if (!validateStep()) return;
    if (currentStepIndex < steps.length - 1) setCurrentStep(steps[currentStepIndex + 1].id);
  }, [validateStep, currentStepIndex, steps]);

  const goPrev = useCallback(() => {
    if (currentStepIndex > 0) setCurrentStep(steps[currentStepIndex - 1].id);
  }, [currentStepIndex, steps]);

  const convertFormDataToApi = useCallback((): CreateListingRequest | UpdateListingRequest => {
    const safeCurrency = (value: string) => {
      if (!value || value.trim() === '') return undefined;
      return parseCurrency(value);
    };
    const safeInt = (value: string) => {
      if (!value || value.trim() === '') return undefined;
      const n = parseInt(value);
      return isNaN(n) ? undefined : n;
    };

    const base: CreateListingRequest | UpdateListingRequest = {
      businessName: formData.businessName.trim(),
      industry: formData.industry.trim(),
      status: formData.status || 'draft',
      listingType: 'business_sale',
      teamVisibility: 'all',
    };

    const askingPrice = safeCurrency(formData.askingPrice);
    if (askingPrice !== undefined) base.askingPrice = askingPrice;

    const cashFlow = safeCurrency(formData.cashFlow);
    if (cashFlow !== undefined) base.cashFlowSde = cashFlow;

    const annualRevenue = safeCurrency(formData.annualRevenue);
    if (annualRevenue !== undefined) base.annualRevenue = annualRevenue;

    if (formData.location?.trim()) base.generalLocation = formData.location.trim();

    const yearEstablished = safeInt(formData.yearEstablished);
    if (yearEstablished !== undefined) base.yearEstablished = yearEstablished;

    const employees = safeInt(formData.employees);
    if (employees !== undefined) base.employees = employees;

    const ownerHours = safeInt(formData.ownerHours);
    if (ownerHours !== undefined) base.ownerHoursWeek = ownerHours;

    const details: any = {};
    let hasDetails = false;

    if (formData.businessDescription?.trim()) {
      details.businessDescription = formData.businessDescription.trim();
      hasDetails = true;
    }
    if (formData.briefDescription?.trim()) {
      details.briefDescription = formData.briefDescription.trim();
      hasDetails = true;
    }

    const fin: any = {};
    const rev2023 = safeCurrency(formData.revenue2023);
    if (rev2023 !== undefined) fin.revenue2023 = rev2023;
    const ebitda = safeCurrency(formData.ebitda2023);
    if (ebitda !== undefined) fin.ebitda = ebitda;
    const inventoryValue = safeCurrency(formData.inventoryValue);
    if (inventoryValue !== undefined) fin.inventoryValue = inventoryValue;
    if (Object.keys(fin).length > 0) {
      details.financialDetails = fin;
      hasDetails = true;
    }

    if (formData.businessModel?.trim()) {
      details.operations = { businessModel: formData.businessModel.trim() };
      hasDetails = true;
    }

    if (formData.keyFeatures?.trim()) {
      const items = formData.keyFeatures.split('\n').map((s) => s.trim()).filter(Boolean);
      details.operations = { ...(details.operations || {}), keyFeatures: items };
      hasDetails = true;
    }

    if (formData.competitiveAdvantages?.trim()) {
      const items = formData.competitiveAdvantages.split('\n').map((s) => s.trim()).filter(Boolean);
      details.operations = { ...(details.operations || {}), competitiveAdvantages: items };
      hasDetails = true;
    }

    if (formData.growthOpportunities?.trim()) {
      details.growthOpportunities = formData.growthOpportunities.split('\n').map((s) => s.trim()).filter(Boolean);
      hasDetails = true;
    }

    if (formData.reasonForSale?.trim()) {
      details.reasonForSale = formData.reasonForSale.trim();
      hasDetails = true;
    }

    // Map real estate status if provided (normalize a couple variants)
    if (formData.realEstateStatus) {
      const map: Record<string, any> = {
        'leased': 'leased',
        'owned': 'owned',
        'available-separately': 'negotiable',
      };
      const realEstateStatus = map[formData.realEstateStatus] || undefined;
      if (realEstateStatus) {
        details.realEstateStatus = realEstateStatus;
        hasDetails = true;
      }
    }

    details.financingAvailable = !!formData.financingAvailable;
    hasDetails = true;

    if (hasDetails) (base as any).details = details;

    return base;
  }, [formData]);

  const handleSaveDraft = useCallback(async () => {
    setIsSubmitting(true);
    try {
      const payload = convertFormDataToApi() as SaveDraftListingRequest;
      payload.status = 'draft';
      payload.listingType = 'business_sale';
      payload.teamVisibility = 'all';

      if (isEditing && listingId) {
        await updateListing.mutateAsync({ listingId, listingData: payload as unknown as UpdateListingRequest });
      } else {
        await saveDraftListing.mutateAsync(payload);
      }
      toast({ title: 'Draft saved' });
    } catch (e: any) {
      toast({ title: 'Error', description: e?.message || 'Failed to save draft', variant: 'destructive' });
    } finally {
      setIsSubmitting(false);
    }
  }, [convertFormDataToApi, isEditing, listingId, updateListing, saveDraftListing]);

  const handleSubmit = useCallback(async () => {
    if (!validateStep()) {
      setCurrentStep('basic');
      return;
    }
    setIsSubmitting(true);
    try {
      const payload = convertFormDataToApi();
      let result;
      if (isEditing && listingId) {
        result = await updateListing.mutateAsync({ listingId, listingData: payload as UpdateListingRequest });
        navigate(`/listings/${listingId}`);
      } else {
        result = await createListing.mutateAsync(payload as CreateListingRequest);
        const id = (result as any)?.data?.id || (result as any)?.id;
        navigate(id ? `/listings/${id}` : '/listings');
      }
      toast({ title: isEditing ? 'Listing updated' : 'Listing created' });
    } catch (e: any) {
      toast({ title: 'Error', description: e?.message || 'Failed to save listing', variant: 'destructive' });
    } finally {
      setIsSubmitting(false);
    }
  }, [validateStep, convertFormDataToApi, isEditing, listingId, updateListing, createListing, navigate]);

  return (
    <div className="max-w-5xl mx-auto space-y-6">
      <Card className="border-0 shadow-xl">
        <CardContent className="p-6">
          <div className="flex items-center justify-between gap-4">
            <div>
              <h2 className="text-2xl font-bold">{isEditing ? 'Edit Listing' : 'Create Listing'}</h2>
              <p className="text-muted-foreground">Step {currentStepIndex + 1} of {steps.length}</p>
            </div>
            <div className="text-right">
              <div className="text-sm text-muted-foreground mb-1">Progress</div>
              <div className="text-xl font-bold text-blue-600">{Math.round(progress)}%</div>
            </div>
          </div>
          <Progress value={progress} className="h-2 mt-4" />

          <div className="mt-6 grid grid-cols-4 gap-3">
            {steps.map((s, idx) => {
              const Icon = s.icon;
              const isActive = s.id === currentStep;
              return (
                <button
                  key={s.id}
                  onClick={() => setCurrentStep(s.id)}
                  className={cn(
                    'group flex items-center gap-3 rounded-lg border p-3 text-left transition-colors',
                    isActive ? 'border-blue-500 bg-blue-50 dark:bg-blue-950/20' : 'hover:bg-muted'
                  )}
                >
                  <div className={cn('rounded-full p-2', isActive ? 'bg-blue-500 text-white' : 'bg-muted text-muted-foreground')}>
                    <Icon className="h-4 w-4" />
                  </div>
                  <div>
                    <div className="text-sm font-medium">{idx + 1}. {s.title}</div>
                    <div className="text-xs text-muted-foreground">{isActive ? 'Current step' : 'Click to edit'}</div>
                  </div>
                </button>
              );
            })}
          </div>
        </CardContent>
      </Card>

      <Card className="border-0 shadow-xl">
        <CardContent className="p-6">
          {currentStep === 'basic' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="businessName">Business Name <span className="text-red-500">*</span></Label>
                <Input id="businessName" value={formData.businessName} onChange={(e) => handleInputChange('businessName', e.target.value)} />
                {validationErrors.businessName && <p className="text-xs text-destructive">{validationErrors.businessName}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="industry">Industry <span className="text-red-500">*</span></Label>
                <Select value={formData.industry} onValueChange={(v) => handleInputChange('industry', v)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select industry" />
                  </SelectTrigger>
                  <SelectContent>
                    {industries.map((i) => (
                      <SelectItem key={i} value={i.toLowerCase()}>{i}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {validationErrors.industry && <p className="text-xs text-destructive">{validationErrors.industry}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="askingPrice">Asking Price <span className="text-red-500">*</span></Label>
                <Input id="askingPrice" value={formData.askingPrice} onChange={(e) => handleCurrencyChange('askingPrice', e.target.value)} placeholder="$0" />
                {validationErrors.askingPrice && <p className="text-xs text-destructive">{validationErrors.askingPrice}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="cashFlow">Cash Flow/SDE <span className="text-red-500">*</span></Label>
                <Input id="cashFlow" value={formData.cashFlow} onChange={(e) => handleCurrencyChange('cashFlow', e.target.value)} placeholder="$0" />
                {validationErrors.cashFlow && <p className="text-xs text-destructive">{validationErrors.cashFlow}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="location">General Location</Label>
                <Input id="location" value={formData.location} onChange={(e) => handleInputChange('location', e.target.value)} placeholder="North Tampa, FL" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select value={formData.status} onValueChange={(v) => handleInputChange('status', v)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {statuses.map((s) => (
                      <SelectItem key={s.value} value={s.value}>
                        <Badge variant={s.value === 'active' ? 'default' : 'secondary'}>{s.label}</Badge>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="col-span-1 md:col-span-2 flex items-center gap-3 rounded-lg border px-3 py-2">
                <Checkbox
                  id="confidential"
                  checked={formData.status === 'confidential'}
                  onCheckedChange={(checked) => handleInputChange('status', checked ? 'confidential' : 'active')}
                />
                <Label htmlFor="confidential">Mark listing as confidential</Label>
              </div>
            </div>
          )}

          {currentStep === 'financial' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="annualRevenue">Annual Revenue</Label>
                <Input id="annualRevenue" value={formData.annualRevenue} onChange={(e) => handleCurrencyChange('annualRevenue', e.target.value)} placeholder="$0" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="revenue2023">2023 Revenue</Label>
                <Input id="revenue2023" value={formData.revenue2023} onChange={(e) => handleCurrencyChange('revenue2023', e.target.value)} placeholder="$0" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="ebitda2023">2023 EBITDA</Label>
                <Input id="ebitda2023" value={formData.ebitda2023} onChange={(e) => handleCurrencyChange('ebitda2023', e.target.value)} placeholder="$0" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="inventoryValue">Inventory Value</Label>
                <Input id="inventoryValue" value={formData.inventoryValue} onChange={(e) => handleCurrencyChange('inventoryValue', e.target.value)} placeholder="$0" />
              </div>
            </div>
          )}

          {currentStep === 'description' && (
            <div className="grid grid-cols-1 gap-6">
              <div className="space-y-2">
                <Label htmlFor="businessDescription">Detailed Business Description</Label>
                <Textarea id="businessDescription" rows={6} value={formData.businessDescription} onChange={(e) => handleInputChange('businessDescription', e.target.value)} />
              </div>
              <div className="space-y-2">
                <Label htmlFor="briefDescription">Brief Description (portfolio)</Label>
                <Textarea id="briefDescription" rows={3} value={formData.briefDescription} onChange={(e) => handleInputChange('briefDescription', e.target.value)} />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="yearEstablished">Year Established</Label>
                  <Input id="yearEstablished" type="number" value={formData.yearEstablished} onChange={(e) => handleInputChange('yearEstablished', e.target.value)} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="employees">Employees</Label>
                  <Input id="employees" type="number" value={formData.employees} onChange={(e) => handleInputChange('employees', e.target.value)} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="ownerHours">Owner Hours/Week</Label>
                  <Input id="ownerHours" type="number" value={formData.ownerHours} onChange={(e) => handleInputChange('ownerHours', e.target.value)} />
                </div>
              </div>
            </div>
          )}

          {currentStep === 'review' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader className="pb-2"><CardTitle className="text-base flex items-center gap-2"><Building2 className="h-4 w-4" /> Basics</CardTitle></CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between text-sm"><span className="text-muted-foreground">Business Name</span><span className="font-medium">{formData.businessName || '—'}</span></div>
                  <div className="flex justify-between text-sm"><span className="text-muted-foreground">Industry</span><span className="font-medium capitalize">{formData.industry || '—'}</span></div>
                  <div className="flex justify-between text-sm"><span className="text-muted-foreground">Asking Price</span><span className="font-medium">{formData.askingPrice || '—'}</span></div>
                  <div className="flex justify-between text-sm"><span className="text-muted-foreground">Cash Flow</span><span className="font-medium">{formData.cashFlow || '—'}</span></div>
                  <div className="flex justify-between text-sm"><span className="text-muted-foreground">Status</span><Badge variant={formData.status === 'active' ? 'default' : 'secondary'}>{statuses.find(s => s.value === formData.status)?.label || formData.status}</Badge></div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2"><CardTitle className="text-base flex items-center gap-2"><DollarSign className="h-4 w-4" /> Financials</CardTitle></CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between text-sm"><span className="text-muted-foreground">Annual Revenue</span><span className="font-medium">{formData.annualRevenue || '—'}</span></div>
                  <div className="flex justify-between text-sm"><span className="text-muted-foreground">2023 Revenue</span><span className="font-medium">{formData.revenue2023 || '—'}</span></div>
                  <div className="flex justify-between text-sm"><span className="text-muted-foreground">2023 EBITDA</span><span className="font-medium">{formData.ebitda2023 || '—'}</span></div>
                </CardContent>
              </Card>
              <Card className="md:col-span-2">
                <CardHeader className="pb-2"><CardTitle className="text-base flex items-center gap-2"><FileText className="h-4 w-4" /> Description</CardTitle></CardHeader>
                <CardContent className="space-y-4 text-sm">
                  <div>
                    <div className="text-muted-foreground">Business Description</div>
                    <div className="mt-1">{formData.businessDescription || '—'}</div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">Brief Description</div>
                    <div className="mt-1">{formData.briefDescription || '—'}</div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </CardContent>
      </Card>

      <div className="flex items-center justify-between">
        {currentStepIndex > 0 ? (
          <Button variant="outline" onClick={goPrev} disabled={isSubmitting} className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" /> Previous
          </Button>
        ) : <div />}

        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleSaveDraft} disabled={isSubmitting} className="flex items-center gap-2">
            {isSubmitting ? <Spinner className="h-4 w-4" /> : <Save className="h-4 w-4" />} Save Draft
          </Button>
          {currentStep !== 'review' ? (
            <Button onClick={goNext} disabled={isSubmitting} className="flex items-center gap-2">
              Next <ArrowRight className="h-4 w-4" />
            </Button>
          ) : (
            <Button onClick={handleSubmit} disabled={isSubmitting} className="flex items-center gap-2 bg-green-600 hover:bg-green-700">
              {isSubmitting ? <Spinner className="h-4 w-4" /> : <Eye className="h-4 w-4" />} {isEditing ? 'Update & View' : 'Create & View'}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}


