import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import {
  AuthCombinedProvider,
  useAuth } from "@/contexts/AuthContext";
import AppLayout from "@/components/layout/app-layout";
import { ProtectedRoute } from "@/components/auth/ProtectedRoute";

// Pages
import V2_Dashboard from "./pages/Dashboard";
import V2_Listings from "./pages/Listings";
import V2_ListingDetail from "./pages/ListingDetail";
import AddListing from "./pages/AddListing";
import EditListing from "./pages/EditListing";
import NotFound from "./pages/NotFound";
import Unauthorized from "./pages/Unauthorized";
import { AuthCallback } from "./pages/AuthCallback";
import Index from "./pages/Index";
import TeamPage from "./pages/TeamPage";
import SessionMonitor from "./components/auth/SessionMonitor";

// import { TokenExpirationSetup } from "./components/TokenExpirationSetup";
import WorkspaceSettingsPage from "./pages/WorkspaceSettings";
import ReportsPage from "./pages/Reports";
import AccountSettingsPage from "./pages/AccountSettings";

const queryClient = new QueryClient();

// Helper component to conditionally apply ProtectedRoute based on environment
const ConditionalProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const isDevelopment = import.meta.env.VITE_NODE_ENV === 'development';

  if (isDevelopment) {
    return <>{children}</>;
  }

  return <ProtectedRoute>{children}</ProtectedRoute>;
};

const AppRoutes = () => {
  const { user } = useAuth();

  return (
    <>
      <SessionMonitor />
      <Routes>
      {/* Public routes */}
      <Route path="/" element={<Index />} />
      <Route path="/unauthorized" element={<Unauthorized />} />
      <Route path="/auth/callback" element={<AuthCallback />} />

      {/* Protected routes with app layout */}
      <Route path="/dashboard" element={<ConditionalProtectedRoute><AppLayout><V2_Dashboard /></AppLayout></ConditionalProtectedRoute>} />
      <Route path="/listings" element={<ConditionalProtectedRoute><AppLayout><V2_Listings /></AppLayout></ConditionalProtectedRoute>} />
      <Route path="/listings/:id" element={<ConditionalProtectedRoute><AppLayout><V2_ListingDetail /></AppLayout></ConditionalProtectedRoute>} />
      <Route path="/listings/:id/edit" element={<ConditionalProtectedRoute><AppLayout><EditListing /></AppLayout></ConditionalProtectedRoute>} />

      <Route path="/team" element={<ConditionalProtectedRoute><AppLayout><TeamPage /></AppLayout></ConditionalProtectedRoute>} />
      {/* Remove ConditionalProtectedRoute wrapper since WorkspaceSettingsPage has its own ProtectedRoute */}
      <Route path="/workspace/settings" element={<AppLayout><WorkspaceSettingsPage /></AppLayout>} />
      <Route path="/listings/new" element={<ConditionalProtectedRoute><AppLayout><AddListing /></AppLayout></ConditionalProtectedRoute>} />
      {/* Remove ConditionalProtectedRoute wrapper since ReportsPage has its own ProtectedRoute */}
      <Route path="/reports" element={<AppLayout><ReportsPage /></AppLayout>} />
      <Route path="/settings" element={<ConditionalProtectedRoute><AppLayout><AccountSettingsPage /></AppLayout></ConditionalProtectedRoute>} />

      {/* Catch all route */}
      <Route path="*" element={<NotFound />} />
    </Routes>
    </>
  );
};

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthCombinedProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          {/* <TokenExpirationSetup /> */}
          <AppRoutes />
        </BrowserRouter>
      </TooltipProvider>
    </AuthCombinedProvider>
  </QueryClientProvider>
);

export default App;
