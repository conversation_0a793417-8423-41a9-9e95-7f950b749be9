import { useQuery, useMutation } from '@tanstack/react-query'
import { apiClient, type CreateListingRequest, type UpdateListingRequest, type SaveDraftListingRequest } from '@/lib/api-client'
import {
  queryKeys,
  handleMutationSuccess,
  handleMutationError,
  useInvalidateQueries,
  useOptimisticUpdates
} from '@/lib/query-client'

// Types
interface UseListingsParams {
  page?: number
  limit?: number
  status?: string
  industry?: string
  search?: string
  location?: string
  assignedTo?: string
  minPrice?: number
  maxPrice?: number
  sortBy?: 'created_at' | 'updated_at' | 'asking_price' | 'business_name' | 'date_listed' | 'days_listed'
  sortOrder?: 'asc' | 'desc'
}

interface MutationOptions {
  showSuccessToast?: boolean
  successMessage?: string
  onSuccess?: (data: any) => void
  onError?: (error: any) => void
}

// QUERIES

// Hook for fetching listings
export function useListListingsQuery(params: UseListingsParams = {}, enabled = true) {
  return useQuery({
    queryKey: queryKeys.listings(params),
    queryFn: async () => {
      return apiClient.getListings({
        page: params.page || 1,
        limit: params.limit || 20,
        status: params.status,
        industry: params.industry,
        search: params.search,
        location: params.location,
        assignedTo: params.assignedTo,
        minPrice: params.minPrice,
        maxPrice: params.maxPrice,
        sortBy: params.sortBy || 'created_at',
        sortOrder: params.sortOrder || 'desc',
      })
    },
    enabled: enabled,
    staleTime: 2 * 60 * 1000, // 2 minutes for listings
  })
}

// Hook for fetching a single listing
export function useGetListingDetailsQuery(listingId: string | undefined, includeDetails = true) {
  return useQuery({
    queryKey: queryKeys.listing(listingId!),
    queryFn: async () => {
      const response = await apiClient.getListing(listingId!, includeDetails)
      return response; // Return the ListingResponse directly (no need to extract .data)
    },
    enabled: !!listingId,
    staleTime: 5 * 60 * 1000, // 5 minutes for individual listings
  })
}

// Hook for fetching user profile
export function useUserProfileQuery(enabled = true) {
  return useQuery({
    queryKey: queryKeys.userProfile(),
    queryFn: async () => {
      return apiClient.getUserProfile()
    },
    enabled: enabled,
    staleTime: 10 * 60 * 1000, // 10 minutes for user profile
  })
}

// Hook for fetching user profile (team data would be handled separately)
export function useUserWorkspaceQuery(enabled = true) {
  return useQuery({
    queryKey: ['user', 'workspace'],
    queryFn: async () => {
      return apiClient.getUserProfile()
    },
    enabled: enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes for workspace data
  })
}

// MUTATIONS

// Hook for creating listings
export function useCreateListingMutation(options: MutationOptions = {}) {
  const { invalidateListings } = useInvalidateQueries()
  const { addListingToCache } = useOptimisticUpdates()

  const {
    showSuccessToast = true,
    successMessage = 'Listing created successfully',
    onSuccess,
    onError,
  } = options

  return useMutation({
    mutationFn: async (listingData: CreateListingRequest) => {
      console.log('Creating listing with cookie authentication');
      return await apiClient.createListing(listingData);
    },
    onSuccess: (data) => {
      if (showSuccessToast) {
        handleMutationSuccess(successMessage)
      }

      // Add to cache optimistically
      if (data) {
        addListingToCache(data)
      }

      // Invalidate listings to ensure fresh data
      invalidateListings()

      if (onSuccess) onSuccess(data)
    },
    onError: (error) => {
      if (showSuccessToast) {
        handleMutationError(error)
      }
      if (onError) onError(error)
    },
  })
}

// Hook for updating listings
export function useUpdateListingMutation(options: MutationOptions = {}) {
  const { invalidateListings, invalidateListing } = useInvalidateQueries()
  const { updateListingData, updateListingsData } = useOptimisticUpdates()

  const {
    showSuccessToast = true,
    successMessage = 'Listing updated successfully',
    onSuccess,
    onError,
  } = options

  return useMutation({
    mutationFn: async ({ listingId, listingData }: { listingId: string; listingData: UpdateListingRequest }) => {
      return apiClient.updateListing(listingId, listingData)
    },
    onSuccess: (data, variables) => {
      if (showSuccessToast) {
        handleMutationSuccess(successMessage)
      }
      
      // Update cache optimistically
      if (data) {
        // Update individual listing cache
        updateListingData(variables.listingId, () => data)
        
        // Update listings list cache
        updateListingsData((oldData: any) => {
          if (!oldData?.data) return oldData
          return {
            ...oldData,
            data: oldData.data.map((listing: any) => 
              listing.id === variables.listingId ? data : listing
            ),
          }
        })
      }
      
      // Invalidate to ensure consistency
      invalidateListing(variables.listingId)
      invalidateListings()
      
      if (onSuccess) onSuccess(data)
    },
    onError: (error) => {
      if (showSuccessToast) {
        handleMutationError(error)
      }
      if (onError) onError(error)
    },
  })
}

// Hook for saving draft listings
export function useSaveDraftListingMutation(options: MutationOptions = {}) {
  const { invalidateListings } = useInvalidateQueries()
  const { addListingToCache } = useOptimisticUpdates()

  const {
    showSuccessToast = true,
    successMessage = 'Draft saved successfully',
    onSuccess,
    onError,
  } = options

  return useMutation({
    mutationFn: async (listingData: SaveDraftListingRequest) => {
      return apiClient.saveDraftListing(listingData)
    },
    onSuccess: (data) => {
      if (showSuccessToast) {
        handleMutationSuccess(successMessage)
      }

      // Add to cache optimistically
      if (data) {
        addListingToCache(data)
      }

      // Invalidate listings to ensure fresh data
      invalidateListings()

      if (onSuccess) onSuccess(data)
    },
    onError: (error) => {
      if (showSuccessToast) {
        handleMutationError(error)
      }
      if (onError) onError(error)
    },
  })
}

// Hook for deleting listings
export function useDeleteListingMutation(options: MutationOptions = {}) {
  const { invalidateListings } = useInvalidateQueries()
  const { removeListingFromCache } = useOptimisticUpdates()

  const {
    showSuccessToast = true,
    successMessage = 'Listing deleted successfully',
    onSuccess,
    onError,
  } = options

  return useMutation({
    mutationFn: async (listingId: string) => {
      return apiClient.deleteListing(listingId)
    },
    onSuccess: (data, listingId) => {
      if (showSuccessToast) {
        handleMutationSuccess(successMessage)
      }

      // Remove from cache optimistically
      removeListingFromCache(listingId)

      // Invalidate listings to ensure fresh data
      invalidateListings()

      if (onSuccess) onSuccess(data)
    },
    onError: (error) => {
      if (showSuccessToast) {
        handleMutationError(error)
      }
      if (onError) onError(error)
    },
  })
}

// Hook for updating listing status
export function useUpdateListingStatusMutation(options: MutationOptions = {}) {
  const { invalidateListings, invalidateListing } = useInvalidateQueries()

  const {
    showSuccessToast = true,
    successMessage = 'Listing status updated successfully',
    onSuccess,
    onError,
  } = options

  return useMutation({
    mutationFn: async ({ listingId, statusUpdate }: { listingId: string; statusUpdate: { status: string; reason?: string; notes?: string } }) => {
      return apiClient.updateListingStatus(listingId, statusUpdate)
    },
    onSuccess: (data, variables) => {
      if (showSuccessToast) {
        handleMutationSuccess(successMessage)
      }

      // Invalidate related queries
      invalidateListing(variables.listingId)
      invalidateListings()

      if (onSuccess) onSuccess(data)
    },
    onError: (error) => {
      if (showSuccessToast) {
        handleMutationError(error)
      }
      if (onError) onError(error)
    },
  })
}

// Hook for updating user profile
export function useUpdateUserProfileMutation(options: MutationOptions = {}) {
  const { invalidateUserProfile } = useInvalidateQueries()

  const {
    showSuccessToast = true,
    successMessage = 'Profile updated successfully',
    onSuccess,
    onError,
  } = options

  return useMutation({
    mutationFn: async (profileData: any) => {
      return apiClient.updateUserProfile(profileData)
    },
    onSuccess: (data) => {
      if (showSuccessToast) {
        handleMutationSuccess(successMessage)
      }

      // Invalidate user profile to refetch fresh data
      invalidateUserProfile()

      if (onSuccess) onSuccess(data)
    },
    onError: (error) => {
      if (showSuccessToast) {
        handleMutationError(error)
      }
      if (onError) onError(error)
    },
  })
}

// Hook for file uploads
export function useUploadFileMutation(options: MutationOptions = {}) {
  const {
    showSuccessToast = true,
    successMessage = 'File uploaded successfully',
    onSuccess,
    onError,
  } = options

  return useMutation({
    mutationFn: async ({ file, options }: { file: File; options: { fileType: 'document' | 'image' | 'video' | 'audio' | 'other'; entityType?: string; entityId?: string; isPublic?: boolean } }) => {
      return apiClient.uploadFile(file, options)
    },
    onSuccess: (data) => {
      if (showSuccessToast) {
        handleMutationSuccess(successMessage)
      }
      if (onSuccess) onSuccess(data)
    },
    onError: (error) => {
      if (showSuccessToast) {
        handleMutationError(error)
      }
      if (onError) onError(error)
    },
  })
}

// Hook for file deletion
export function useDeleteFileMutation(options: MutationOptions = {}) {
  const {
    showSuccessToast = true,
    successMessage = 'File deleted successfully',
    onSuccess,
    onError,
  } = options

  return useMutation({
    mutationFn: async (fileId: string) => {
      return apiClient.deleteFile(fileId)
    },
    onSuccess: (data) => {
      if (showSuccessToast) {
        handleMutationSuccess(successMessage)
      }
      if (onSuccess) onSuccess(data)
    },
    onError: (error) => {
      if (showSuccessToast) {
        handleMutationError(error)
      }
      if (onError) onError(error)
    },
  })
}

// Hook for CSV bulk import
export function useImportListingsCsvMutation(options: MutationOptions = {}) {
  const { invalidateListings } = useInvalidateQueries()

  const {
    showSuccessToast = true,
    successMessage = 'CSV import completed successfully',
    onSuccess,
    onError,
  } = options

  return useMutation({
    mutationFn: async (file: File) => {
      return apiClient.importListingsFromCSV(file)
    },
    onSuccess: (data) => {
      if (showSuccessToast) {
        handleMutationSuccess(successMessage)
      }

      // Invalidate listings to ensure fresh data
      invalidateListings()

      if (onSuccess) onSuccess(data)
    },
    onError: (error) => {
      if (showSuccessToast) {
        handleMutationError(error)
      }
      if (onError) onError(error)
    },
  })
}

// CONVENIENCE HOOKS

// Combined hook for listings operations (replaces useListings)
export function useListings(params: UseListingsParams = {}) {
  const listingsQuery = useListListingsQuery(params)
  const createMutation = useCreateListingMutation()
  const updateMutation = useUpdateListingMutation()
  const deleteMutation = useDeleteListingMutation()
  const updateStatusMutation = useUpdateListingStatusMutation()

  return {
    // Data
    listings: listingsQuery.data?.data || [],
    pagination: listingsQuery.data?.pagination || { page: 1, limit: 20, total: 0, pages: 0 },
    
    // Loading states
    loading: listingsQuery.isLoading,
    isRefetching: listingsQuery.isRefetching,
    
    // Error states
    error: listingsQuery.error,
    
    // Query state
    isSuccess: listingsQuery.isSuccess,
    isError: listingsQuery.isError,
    
    // Functions
    refetch: listingsQuery.refetch,
    
    // Mutations
    createListing: createMutation.mutate,
    updateListing: (listingId: string, listingData: UpdateListingRequest) => 
      updateMutation.mutate({ listingId, listingData }),
    deleteListing: deleteMutation.mutate,
    updateListingStatus: (listingId: string, status: string, reason?: string, notes?: string) => 
      updateStatusMutation.mutate({ listingId, statusUpdate: { status, reason, notes } }),
    
    // Mutation states
    mutations: {
      creating: createMutation.isPending,
      updating: updateMutation.isPending,
      deleting: deleteMutation.isPending,
      updatingStatus: updateStatusMutation.isPending,
      
      createError: createMutation.error,
      updateError: updateMutation.error,
      deleteError: deleteMutation.error,
      statusError: updateStatusMutation.error,
    },
  }
}

// Single listing hook (replaces useListing)
export function useListing(listingId: string | undefined) {
  const listingQuery = useGetListingDetailsQuery(listingId)
  
  return {
    listing: listingQuery.data || null,
    loading: listingQuery.isLoading,
    error: listingQuery.error,
    isSuccess: listingQuery.isSuccess,
    isError: listingQuery.isError,
    refetch: listingQuery.refetch,
  }
} 